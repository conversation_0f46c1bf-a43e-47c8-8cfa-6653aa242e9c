{"name": "odude-sdk", "version": "2.0.0", "description": "Multi-network SDK for ODude smart contracts ecosystem with automatic TLD-based routing, batch operations, and comprehensive error handling", "main": "src/index.js", "scripts": {"test": "mocha test/**/*.test.js --timeout 10000", "test:watch": "mocha test/**/*.test.js --watch", "test:names": "mocha test/names.test.js --timeout 10000", "test:multi-network": "mocha test/multi-network.test.js --timeout 10000", "test:tld-management": "mocha test/tld-management.test.js --timeout 10000", "test:batch": "mocha test/batch-operations.test.js --timeout 10000", "lint": "eslint src test", "verify": "node scripts/verify-setup.js", "sample": "node od_sample.js", "example:basic": "node examples/basic-usage.js", "example:resolve": "node examples/resolve-names.js", "example:airdrop": "node examples/check-airdrop.js", "example:tld": "node examples/tld-management.js"}, "keywords": ["odude", "blockchain", "ethereum", "web3", "smart-contracts", "naming-service", "dns", "ens", "domain", "nft", "airdrop", "ethers", "web3-sdk", "multi-network", "filecoin", "bnb", "base-sepolia", "batch-operations", "tld-management", "domain-minting", "event-monitoring"], "author": "ODude Team", "license": "MIT", "dependencies": {"ethers": "^6.13.0"}, "devDependencies": {"chai": "^4.3.10", "mocha": "^10.2.0", "eslint": "^8.54.0"}, "engines": {"node": ">=16.0.0"}, "repository": {"type": "git", "url": "https://github.com/odude/odude-sdk"}, "bugs": {"url": "https://github.com/odude/odude-sdk/issues"}, "homepage": "https://github.com/odude/odude-sdk#readme", "files": ["src/", "abi/", "config/", "localhost-deployment.json", "README.md", "LICENSE"]}