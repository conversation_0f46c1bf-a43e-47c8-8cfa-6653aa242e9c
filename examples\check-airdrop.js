/**
 * Airdrop Check Example
 * Demonstrates how to check airdrop eligibility and claim status
 */

const ODudeSDK = require('../src/index');

async function main() {
  console.log('=== ODude Airdrop Check Example ===\n');

  // Initialize SDK
  const sdk = new ODudeSDK({
    rpcUrl: 'http://127.0.0.1:8545'
  });

  sdk.connectLocalhost();
  console.log('✓ Connected to contracts\n');

  // Test addresses (Hardhat default accounts)
  const testAddresses = [
    '******************************************',
    '******************************************',
    '******************************************'
  ];

  console.log('--- Airdrop Status ---');
  try {
    const isActive = await sdk.rwairdrop.isAirdropActive();
    console.log('Airdrop Active:', isActive);
    
    const totalClaimed = await sdk.rwairdrop.getTotalClaimed();
    const totalAmount = await sdk.rwairdrop.getTotalAirdropAmount();
    
    console.log('Total Claimed:', sdk.utils.formatEther(totalClaimed));
    console.log('Total Amount:', sdk.utils.formatEther(totalAmount));
    
    const startTime = await sdk.rwairdrop.getStartTime();
    const endTime = await sdk.rwairdrop.getEndTime();
    
    console.log('Start Time:', new Date(Number(startTime) * 1000).toISOString());
    console.log('End Time:', new Date(Number(endTime) * 1000).toISOString());
  } catch (error) {
    console.log('Failed to get airdrop status:', error.message);
  }

  console.log('\n--- Checking Addresses ---');
  
  for (const address of testAddresses) {
    console.log(`\nAddress: ${address}`);
    
    try {
      // Get comprehensive airdrop info
      const info = await sdk.getAirdropInfo(address);
      
      console.log('  Eligible:', info.eligible);
      console.log('  Claimed:', info.claimed);
      console.log('  Claimable Amount:', sdk.utils.formatEther(info.claimableAmount));
      console.log('  Airdrop Active:', info.isActive);
      console.log('  Can Claim:', info.canClaim);
      
      if (info.canClaim) {
        console.log('  ✓ This address can claim the airdrop!');
      } else if (info.claimed) {
        console.log('  ✗ Already claimed');
      } else if (!info.eligible) {
        console.log('  ✗ Not eligible');
      } else if (!info.isActive) {
        console.log('  ✗ Airdrop not active');
      }
    } catch (error) {
      console.log('  Error:', error.message);
    }
  }

  console.log('\n--- Individual Checks ---');
  
  const checkAddress = testAddresses[0];
  console.log(`\nDetailed check for: ${checkAddress}`);
  
  try {
    const isEligible = await sdk.rwairdrop.isEligible(checkAddress);
    const hasClaimed = await sdk.rwairdrop.hasClaimed(checkAddress);
    const claimableAmount = await sdk.rwairdrop.getClaimableAmount(checkAddress);
    
    console.log('Is Eligible:', isEligible);
    console.log('Has Claimed:', hasClaimed);
    console.log('Claimable Amount:', sdk.utils.formatEther(claimableAmount), 'tokens');
    
    if (isEligible && !hasClaimed && claimableAmount > 0n) {
      console.log('\n💰 Ready to claim!');
      console.log('To claim, use:');
      console.log('  const tx = await sdk.rwairdrop.claim();');
      console.log('  await tx.wait();');
    }
  } catch (error) {
    console.log('Error during detailed check:', error.message);
  }

  console.log('\n✓ Example completed!');
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error('Error:', error);
    process.exit(1);
  });

