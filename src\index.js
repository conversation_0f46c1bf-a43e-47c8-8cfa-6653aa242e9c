/**
 * ODude SDK - Main export file
 * Developer-friendly SDK for interacting with ODude smart contracts
 */

const ODudeSDK = require('./ODudeSDK');
const Registry = require('./contracts/Registry');
const Resolver = require('./contracts/Resolver');
const TLD = require('./contracts/TLD');
const RWAirdrop = require('./contracts/RWAirdrop');
const helpers = require('./utils/helpers');

// Export main SDK class
module.exports = ODudeSDK;

// Export individual contract wrappers
module.exports.Registry = Registry;
module.exports.Resolver = Resolver;
module.exports.TLD = TLD;
module.exports.RWAirdrop = RWAirdrop;

// Export utilities
module.exports.utils = helpers;

// Export default
module.exports.default = ODudeSDK;

