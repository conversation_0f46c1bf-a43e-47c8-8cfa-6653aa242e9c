const { Contract } = require('ethers');
const RWAirdropABI = require('../../abi/RWAirdrop.json');

/**
 * RWAirdrop contract wrapper
 * Handles airdrop claims and management
 */
class RWAirdrop {
  /**
   * @param {string} address - Contract address
   * @param {ethers.Provider|ethers.Signer} providerOrSigner - Ethers provider or signer
   */
  constructor(address, providerOrSigner) {
    this.address = address;
    this.contract = new Contract(address, RWAirdropABI.abi, providerOrSigner);
  }

  // ==================== Read-only Functions ====================

  /**
   * Check if an address has claimed
   * @param {string} address - Ethereum address
   * @returns {Promise<boolean>}
   */
  async hasClaimed(address) {
    return await this.contract.hasClaimed(address);
  }

  /**
   * Get claimable amount for an address
   * @param {string} address - Ethereum address
   * @returns {Promise<bigint>} Claimable amount in wei
   */
  async getClaimableAmount(address) {
    return await this.contract.getClaimableAmount(address);
  }

  /**
   * Check if an address is eligible for airdrop
   * @param {string} address - Ethereum address
   * @returns {Promise<boolean>}
   */
  async isEligible(address) {
    return await this.contract.isEligible(address);
  }

  /**
   * Get total claimed amount
   * @returns {Promise<bigint>}
   */
  async getTotalClaimed() {
    return await this.contract.getTotalClaimed();
  }

  /**
   * Get total airdrop amount
   * @returns {Promise<bigint>}
   */
  async getTotalAirdropAmount() {
    return await this.contract.getTotalAirdropAmount();
  }

  /**
   * Get airdrop token address
   * @returns {Promise<string>}
   */
  async getAirdropToken() {
    return await this.contract.getAirdropToken();
  }

  /**
   * Check if airdrop is active
   * @returns {Promise<boolean>}
   */
  async isAirdropActive() {
    return await this.contract.isAirdropActive();
  }

  /**
   * Get airdrop start time
   * @returns {Promise<bigint>} Unix timestamp
   */
  async getStartTime() {
    return await this.contract.getStartTime();
  }

  /**
   * Get airdrop end time
   * @returns {Promise<bigint>} Unix timestamp
   */
  async getEndTime() {
    return await this.contract.getEndTime();
  }

  /**
   * Get contract owner
   * @returns {Promise<string>}
   */
  async owner() {
    return await this.contract.owner();
  }

  /**
   * Get contract balance
   * @returns {Promise<bigint>}
   */
  async getBalance() {
    return await this.contract.getBalance();
  }

  // ==================== Write Functions ====================

  /**
   * Claim airdrop
   * @param {Object} options - Transaction options
   * @returns {Promise<Object>} Transaction response
   */
  async claim(options = {}) {
    return await this.contract.claim(options);
  }

  /**
   * Claim airdrop with proof (for merkle tree airdrops)
   * @param {Array<string>} proof - Merkle proof
   * @param {bigint|string} amount - Amount to claim
   * @param {Object} options - Transaction options
   * @returns {Promise<Object>} Transaction response
   */
  async claimWithProof(proof, amount, options = {}) {
    return await this.contract.claimWithProof(proof, amount, options);
  }

  /**
   * Set airdrop active status (only owner)
   * @param {boolean} active - Active status
   * @returns {Promise<Object>} Transaction response
   */
  async setAirdropActive(active) {
    return await this.contract.setAirdropActive(active);
  }

  /**
   * Set airdrop times (only owner)
   * @param {bigint|number} startTime - Start time (unix timestamp)
   * @param {bigint|number} endTime - End time (unix timestamp)
   * @returns {Promise<Object>} Transaction response
   */
  async setAirdropTimes(startTime, endTime) {
    return await this.contract.setAirdropTimes(startTime, endTime);
  }

  /**
   * Add eligible addresses (only owner)
   * @param {Array<string>} addresses - Array of addresses
   * @param {Array<bigint|string>} amounts - Array of amounts
   * @returns {Promise<Object>} Transaction response
   */
  async addEligibleAddresses(addresses, amounts) {
    return await this.contract.addEligibleAddresses(addresses, amounts);
  }

  /**
   * Remove eligible address (only owner)
   * @param {string} address - Address to remove
   * @returns {Promise<Object>} Transaction response
   */
  async removeEligibleAddress(address) {
    return await this.contract.removeEligibleAddress(address);
  }

  /**
   * Withdraw tokens (only owner)
   * @param {string} tokenAddress - Token address
   * @param {bigint|string} amount - Amount to withdraw
   * @returns {Promise<Object>} Transaction response
   */
  async withdrawTokens(tokenAddress, amount) {
    return await this.contract.withdrawTokens(tokenAddress, amount);
  }

  /**
   * Withdraw ETH (only owner)
   * @returns {Promise<Object>} Transaction response
   */
  async withdraw() {
    return await this.contract.withdraw();
  }

  // ==================== Events ====================

  /**
   * Listen to Claimed events
   * @param {Function} callback - Callback function
   */
  onClaimed(callback) {
    this.contract.on('Claimed', callback);
  }

  /**
   * Listen to AirdropActiveSet events
   * @param {Function} callback - Callback function
   */
  onAirdropActiveSet(callback) {
    this.contract.on('AirdropActiveSet', callback);
  }

  /**
   * Listen to EligibleAddressAdded events
   * @param {Function} callback - Callback function
   */
  onEligibleAddressAdded(callback) {
    this.contract.on('EligibleAddressAdded', callback);
  }

  /**
   * Remove all listeners
   */
  removeAllListeners() {
    this.contract.removeAllListeners();
  }

  // ==================== Helper Functions ====================

  /**
   * Get comprehensive airdrop info for an address
   * @param {string} address - Ethereum address
   * @returns {Promise<Object>} Airdrop info
   */
  async getAirdropInfo(address) {
    const [eligible, claimed, claimableAmount, isActive] = await Promise.all([
      this.isEligible(address),
      this.hasClaimed(address),
      this.getClaimableAmount(address),
      this.isAirdropActive()
    ]);

    return {
      eligible,
      claimed,
      claimableAmount,
      isActive,
      canClaim: eligible && !claimed && isActive && claimableAmount > 0n
    };
  }
}

module.exports = RWAirdrop;

